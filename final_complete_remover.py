#!/usr/bin/env python3
"""
最终综合去除工具 - 同时真正去除FlexTV水印和底部字幕
结合最佳的水印修复技术和字幕去除方法
"""

import subprocess
import os
import json
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def get_regions(width, height):
    """获取水印和字幕的精确区域"""
    # FlexTV水印区域（右上角）
    watermark_region = {
        'x': int(width * 0.87),  # 右边13%处开始
        'y': 15,
        'width': int(width * 0.12),
        'height': int(height * 0.06)
    }
    
    # 字幕区域（底部）
    subtitle_region = {
        'x': 0,
        'y': int(height * 0.80),  # 底部20%
        'width': width,
        'height': int(height * 0.20)
    }
    
    return watermark_region, subtitle_region

def remove_both_delogo_advanced(input_path, output_path, watermark_region, subtitle_region):
    """
    使用高级delogo同时去除水印和字幕
    """
    wx, wy, ww, wh = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    sx, sy, sw, sh = subtitle_region['x'], subtitle_region['y'], subtitle_region['width'], subtitle_region['height']
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={wx}:y={wy}:w={ww}:h={wh}:show=0,delogo=x={sx}:y={sy}:w={sw}:h={sh}:show=0',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 高级Delogo同时去除水印和字幕")
    print(f"   水印区域: ({wx}, {wy}) - {ww}x{wh}")
    print(f"   字幕区域: ({sx}, {sy}) - {sw}x{sh}")
    
    return run_ffmpeg_with_progress(cmd, "高级Delogo同时去除")

def remove_both_background_fill(input_path, output_path, watermark_region, subtitle_region):
    """
    背景填充方法同时处理水印和字幕
    """
    wx, wy, ww, wh = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    sx, sy, sw, sh = subtitle_region['x'], subtitle_region['y'], subtitle_region['width'], subtitle_region['height']
    
    # 水印背景填充
    expand_size = 30
    sample_x = max(0, wx - expand_size)
    sample_y = max(0, wy - expand_size)
    sample_w = ww + expand_size * 2
    sample_h = wh + expand_size * 2
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'split=3[main][wbg][sbg];[wbg]crop={sample_w}:{sample_h}:{sample_x}:{sample_y},boxblur=15:1,crop={ww}:{wh}:{expand_size}:{expand_size}[wfill];[sbg]crop={sw}:{sh*2}:{sx}:{max(0,sy-sh)}:boxblur=20:1,crop={sw}:{sh}:0:{sh//2}[sfill];[main][wfill]overlay={wx}:{wy}[tmp];[tmp][sfill]overlay={sx}:{sy}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 背景填充同时去除水印和字幕")
    print(f"   水印填充: ({wx}, {wy}) - {ww}x{wh}")
    print(f"   字幕填充: ({sx}, {sy}) - {sw}x{sh}")
    
    return run_ffmpeg_with_progress(cmd, "背景填充同时去除")

def remove_both_hybrid_method(input_path, output_path, watermark_region, subtitle_region):
    """
    混合方法：水印用delogo，字幕用裁剪+缩放
    """
    wx, wy, ww, wh = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    sx, sy, sw, sh = subtitle_region['x'], subtitle_region['y'], subtitle_region['width'], subtitle_region['height']
    
    # 计算裁剪后的高度
    crop_height = sy  # 裁剪到字幕开始的位置
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={wx}:y={wy}:w={ww}:h={wh}:show=0,crop={sw}:{crop_height}:{sx}:0,scale={sw}:{sy+sh}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🔄 混合方法去除水印和字幕")
    print(f"   水印Delogo: ({wx}, {wy}) - {ww}x{wh}")
    print(f"   字幕裁剪: 保留上部{crop_height}像素，缩放到原高度")
    
    return run_ffmpeg_with_progress(cmd, "混合方法去除")

def remove_both_smart_cover(input_path, output_path, watermark_region, subtitle_region):
    """
    智能覆盖：水印用半透明覆盖，字幕用黑色覆盖
    """
    wx, wy, ww, wh = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    sx, sy, sw, sh = subtitle_region['x'], subtitle_region['y'], subtitle_region['width'], subtitle_region['height']
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x={wx}:y={wy}:w={ww}:h={wh}:color=0x1a1a1a@0.8:t=fill,drawbox=x={sx}:y={sy}:w={sw}:h={sh}:color=black:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎭 智能覆盖去除水印和字幕")
    print(f"   水印覆盖: ({wx}, {wy}) - {ww}x{wh} (半透明)")
    print(f"   字幕覆盖: ({sx}, {sy}) - {sw}x{sh} (黑色)")
    
    return run_ffmpeg_with_progress(cmd, "智能覆盖去除")

def remove_watermark_only_best(input_path, output_path, watermark_region):
    """
    只去除水印（最佳方法）
    """
    wx, wy, ww, wh = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={wx}:y={wy}:w={ww}:h={wh}:show=0',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 只去除水印（最佳方法）")
    print(f"   水印区域: ({wx}, {wy}) - {ww}x{wh}")
    
    return run_ffmpeg_with_progress(cmd, "只去除水印")

def remove_subtitle_only_best(input_path, output_path, subtitle_region):
    """
    只去除字幕（最佳方法）
    """
    sx, sy, sw, sh = subtitle_region['x'], subtitle_region['y'], subtitle_region['width'], subtitle_region['height']
    
    # 使用裁剪+缩放方法
    crop_height = sy
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={sw}:{crop_height}:{sx}:0,scale={sw}:{sy+sh}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"✂️  只去除字幕（最佳方法）")
    print(f"   裁剪高度: {crop_height}，缩放到原高度")
    
    return run_ffmpeg_with_progress(cmd, "只去除字幕")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🎬 最终综合去除工具 (真正修复水印 + 去除字幕)")
    print("=" * 60)

    # 使用原始视频文件
    input_video = "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 获取精确区域
    watermark_region, subtitle_region = get_regions(width, height)
    print(f"🎯 水印区域: ({watermark_region['x']}, {watermark_region['y']}) - {watermark_region['width']}x{watermark_region['height']}")
    print(f"📝 字幕区域: ({subtitle_region['x']}, {subtitle_region['y']}) - {subtitle_region['width']}x{subtitle_region['height']}")
    
    # 定义处理方法
    methods = [
        {
            'name': 'both_delogo',
            'description': '同时去除(Delogo修复)',
            'output': '/Users/<USER>/Work/Script/temp3/final_both_delogo.mp4',
            'function': lambda: remove_both_delogo_advanced(input_video, '/Users/<USER>/Work/Script/temp3/final_both_delogo.mp4', watermark_region, subtitle_region)
        },
        {
            'name': 'both_hybrid',
            'description': '同时去除(混合方法)',
            'output': '/Users/<USER>/Work/Script/temp3/final_both_hybrid.mp4',
            'function': lambda: remove_both_hybrid_method(input_video, '/Users/<USER>/Work/Script/temp3/final_both_hybrid.mp4', watermark_region, subtitle_region)
        },
        {
            'name': 'both_cover',
            'description': '同时去除(智能覆盖)',
            'output': '/Users/<USER>/Work/Script/temp3/final_both_cover.mp4',
            'function': lambda: remove_both_smart_cover(input_video, '/Users/<USER>/Work/Script/temp3/final_both_cover.mp4', watermark_region, subtitle_region)
        },
        {
            'name': 'watermark_only',
            'description': '只去除水印',
            'output': '/Users/<USER>/Work/Script/temp3/final_watermark_only.mp4',
            'function': lambda: remove_watermark_only_best(input_video, '/Users/<USER>/Work/Script/temp3/final_watermark_only.mp4', watermark_region)
        },
        {
            'name': 'subtitle_only',
            'description': '只去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_subtitle_only.mp4',
            'function': lambda: remove_subtitle_only_best(input_video, '/Users/<USER>/Work/Script/temp3/final_subtitle_only.mp4', subtitle_region)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 最终处理结果:")
    print("-" * 70)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<25}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                              📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 最终推荐
    print(f"\n🏆 最终推荐:")
    print("-" * 70)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("   🥇 final_both_delogo.mp4 (同时去除，Delogo修复)")
        print("   🥈 final_both_hybrid.mp4 (混合方法)")
        print("   🥉 final_watermark_only.mp4 (只去除水印)")
        print("   4️⃣  final_subtitle_only.mp4 (只去除字幕)")
        print("   5️⃣  final_both_cover.mp4 (智能覆盖)")
        
        print(f"\n💡 最终建议:")
        print("• final_both_delogo.mp4 是最推荐的版本")
        print("• 使用真正的修复算法，不是简单裁剪")
        print("• 保持原始视频比例和质量")
        print("• 如果效果不满意，可尝试其他版本")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装")
    
    print(f"\n🎉 最终综合处理完成!")
    print(f"📁 所有文件保存在: /Users/<USER>/Work/Script/temp3/")

if __name__ == "__main__":
    main()
