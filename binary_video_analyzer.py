#!/usr/bin/env python3
"""
二进制视频分析工具 - 从二进制层面分析和操作视频文件
尝试在二进制层面识别和去除水印元素
"""

import os
import struct
import hashlib
from collections import Counter
import subprocess
import json

def get_video_info(video_path):
    """获取视频基本信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0)),
                        'codec': stream.get('codec_name', 'unknown'),
                        'bitrate': int(stream.get('bit_rate', 0)) if stream.get('bit_rate') else 0
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def analyze_binary_structure(file_path, chunk_size=1024*1024):
    """分析视频文件的二进制结构"""
    print(f"🔍 分析文件二进制结构: {os.path.basename(file_path)}")
    
    file_size = os.path.getsize(file_path)
    print(f"📊 文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
    
    # 分析文件头
    with open(file_path, 'rb') as f:
        # 读取前1KB作为文件头
        header = f.read(1024)
        print(f"📋 文件头 (前32字节): {header[:32].hex()}")
        
        # 检测文件格式
        if header.startswith(b'\x00\x00\x00'):
            print("📹 检测到: MP4/MOV格式")
            analyze_mp4_structure(f, file_size)
        elif header.startswith(b'FLV'):
            print("📹 检测到: FLV格式")
        elif header.startswith(b'\x1a\x45\xdf\xa3'):
            print("📹 检测到: MKV格式")
        else:
            print("📹 未知格式，进行通用分析")
        
        # 字节频率分析
        f.seek(0)
        byte_counter = Counter()
        bytes_analyzed = 0
        
        while bytes_analyzed < min(file_size, 10*1024*1024):  # 分析前10MB
            chunk = f.read(chunk_size)
            if not chunk:
                break
            byte_counter.update(chunk)
            bytes_analyzed += len(chunk)
        
        print(f"📈 字节频率分析 (前{bytes_analyzed:,}字节):")
        most_common = byte_counter.most_common(10)
        for byte_val, count in most_common:
            percentage = (count / bytes_analyzed) * 100
            print(f"   0x{byte_val:02X}: {count:,} 次 ({percentage:.2f}%)")

def analyze_mp4_structure(file_handle, file_size):
    """分析MP4文件的box结构"""
    print("🎬 分析MP4 Box结构:")
    
    file_handle.seek(0)
    position = 0
    box_count = 0
    
    while position < file_size and box_count < 20:  # 限制分析前20个box
        try:
            # 读取box头部 (size + type)
            box_header = file_handle.read(8)
            if len(box_header) < 8:
                break
                
            size, box_type = struct.unpack('>I4s', box_header)
            box_type_str = box_type.decode('ascii', errors='ignore')
            
            print(f"   📦 Box: {box_type_str} | 大小: {size:,} bytes | 位置: 0x{position:08X}")
            
            # 跳到下一个box
            if size == 0:  # 表示box延续到文件末尾
                break
            elif size == 1:  # 64位大小
                extended_size = struct.unpack('>Q', file_handle.read(8))[0]
                file_handle.seek(position + extended_size)
                position += extended_size
            else:
                file_handle.seek(position + size)
                position += size
                
            box_count += 1
            
        except Exception as e:
            print(f"   ❌ Box解析错误: {e}")
            break

def search_pattern_in_binary(file_path, patterns, context_size=64):
    """在二进制文件中搜索特定模式"""
    print(f"🔎 搜索二进制模式...")
    
    results = []
    
    with open(file_path, 'rb') as f:
        file_size = os.path.getsize(file_path)
        chunk_size = 1024 * 1024  # 1MB chunks
        overlap = max(len(p) for p in patterns) + context_size
        
        position = 0
        prev_chunk_end = b''
        
        while position < file_size:
            # 读取chunk，包含与前一个chunk的重叠
            chunk = prev_chunk_end + f.read(chunk_size)
            if not chunk:
                break
            
            # 搜索每个模式
            for pattern_name, pattern in patterns.items():
                if isinstance(pattern, str):
                    pattern = pattern.encode()
                
                offset = 0
                while True:
                    found_pos = chunk.find(pattern, offset)
                    if found_pos == -1:
                        break
                    
                    absolute_pos = position + found_pos - len(prev_chunk_end)
                    
                    # 获取上下文
                    context_start = max(0, found_pos - context_size)
                    context_end = min(len(chunk), found_pos + len(pattern) + context_size)
                    context = chunk[context_start:context_end]
                    
                    results.append({
                        'pattern': pattern_name,
                        'position': absolute_pos,
                        'context': context,
                        'hex_context': context.hex()
                    })
                    
                    print(f"   ✅ 找到 '{pattern_name}' 在位置 0x{absolute_pos:08X}")
                    print(f"      上下文: {context[:32].hex()}...")
                    
                    offset = found_pos + 1
            
            # 准备下一次迭代
            position += len(chunk) - len(prev_chunk_end)
            prev_chunk_end = chunk[-overlap:] if len(chunk) >= overlap else chunk
    
    return results

def extract_frames_as_binary(video_path, output_dir, frame_count=5):
    """提取视频帧为二进制数据进行分析"""
    print(f"🎞️  提取帧进行二进制分析...")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取几帧为PNG格式
    for i in range(frame_count):
        timestamp = i * 10  # 每10秒提取一帧
        output_path = os.path.join(output_dir, f"frame_{i:03d}.png")
        
        cmd = [
            'ffmpeg', '-i', video_path,
            '-ss', str(timestamp),
            '-vframes', '1',
            '-y',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✅ 提取帧 {i}: {os.path.basename(output_path)}")
                
                # 分析帧的二进制特征
                analyze_frame_binary(output_path)
            else:
                print(f"   ❌ 提取帧 {i} 失败")
        except Exception as e:
            print(f"   ❌ 提取帧 {i} 错误: {e}")

def analyze_frame_binary(frame_path):
    """分析单个帧的二进制特征"""
    with open(frame_path, 'rb') as f:
        data = f.read()
        
        # PNG文件结构分析
        if data.startswith(b'\x89PNG\r\n\x1a\n'):
            print(f"      📸 PNG帧大小: {len(data)} bytes")
            
            # 查找PNG chunks
            pos = 8  # 跳过PNG签名
            while pos < len(data) - 8:
                try:
                    length = struct.unpack('>I', data[pos:pos+4])[0]
                    chunk_type = data[pos+4:pos+8].decode('ascii', errors='ignore')
                    print(f"         📦 PNG Chunk: {chunk_type} ({length} bytes)")
                    pos += 8 + length + 4  # length + type + data + CRC
                except:
                    break

def create_watermark_removal_binary(input_path, output_path, patterns_to_remove):
    """尝试通过二进制操作去除水印"""
    print(f"🔧 尝试二进制水印去除...")
    
    with open(input_path, 'rb') as input_file:
        data = input_file.read()
    
    original_size = len(data)
    modifications = 0
    
    # 替换找到的模式
    for pattern_name, pattern_info in patterns_to_remove.items():
        if 'pattern' in pattern_info and 'replacement' in pattern_info:
            pattern = pattern_info['pattern']
            replacement = pattern_info['replacement']
            
            if isinstance(pattern, str):
                pattern = pattern.encode()
            if isinstance(replacement, str):
                replacement = replacement.encode()
            
            # 确保替换数据长度相同
            if len(replacement) != len(pattern):
                replacement = replacement[:len(pattern)].ljust(len(pattern), b'\x00')
            
            old_data = data
            data = data.replace(pattern, replacement)
            
            if data != old_data:
                modifications += 1
                print(f"   ✅ 替换模式 '{pattern_name}': {len(pattern)} bytes")
    
    # 保存修改后的文件
    if modifications > 0:
        with open(output_path, 'wb') as output_file:
            output_file.write(data)
        
        print(f"   💾 保存修改后的文件: {os.path.basename(output_path)}")
        print(f"   📊 原始大小: {original_size:,} bytes")
        print(f"   📊 修改后大小: {len(data):,} bytes")
        print(f"   🔄 进行了 {modifications} 次修改")
        
        return True
    else:
        print(f"   ❌ 未找到可替换的模式")
        return False

def main():
    print("🔬 二进制视频分析和水印去除工具")
    print("=" * 60)
    
    # 输入文件
    input_video = "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if video_info:
        print(f"✅ 视频信息:")
        print(f"   分辨率: {video_info['width']}x{video_info['height']}")
        print(f"   编码: {video_info['codec']}")
        print(f"   时长: {video_info['duration']:.1f}s")
        print(f"   比特率: {video_info['bitrate']:,} bps")
    
    # 分析二进制结构
    analyze_binary_structure(input_video)
    
    # 定义要搜索的模式（可能与FlexTV水印相关）
    search_patterns = {
        'FlexTV_text': 'FlexTV',
        'Flex_text': 'Flex',
        'TV_text': 'TV',
        # PNG图像头（水印可能是PNG格式）
        'PNG_header': b'\x89PNG\r\n\x1a\n',
        # JPEG图像头
        'JPEG_header': b'\xff\xd8\xff',
        # 常见的水印相关字符串
        'copyright': 'copyright',
        'watermark': 'watermark',
    }
    
    # 搜索模式
    found_patterns = search_pattern_in_binary(input_video, search_patterns)
    
    # 提取帧进行分析
    frames_dir = "/Users/<USER>/Work/Script/temp3/binary_analysis_frames"
    extract_frames_as_binary(input_video, frames_dir)
    
    # 如果找到了相关模式，尝试去除
    if found_patterns:
        print(f"\n🎯 找到 {len(found_patterns)} 个可能的水印相关模式")
        
        # 定义替换规则（这里是示例，实际需要根据分析结果调整）
        removal_patterns = {}
        
        for result in found_patterns:
            if result['pattern'] in ['FlexTV_text', 'Flex_text']:
                # 用空格或null字节替换文本
                removal_patterns[result['pattern']] = {
                    'pattern': search_patterns[result['pattern']],
                    'replacement': b'\x00' * len(search_patterns[result['pattern']])
                }
        
        if removal_patterns:
            output_path = "/Users/<USER>/Work/Script/temp3/binary_modified_video.mp4"
            success = create_watermark_removal_binary(input_video, output_path, removal_patterns)
            
            if success:
                print(f"\n✅ 二进制修改完成!")
                print(f"📁 输出文件: {os.path.basename(output_path)}")
                print(f"\n⚠️  注意: 二进制修改可能会损坏视频文件")
                print(f"   建议使用视频播放器测试修改后的文件")
        else:
            print(f"\n❌ 未找到可以安全替换的文本模式")
    else:
        print(f"\n❌ 未找到明显的水印相关模式")
    
    print(f"\n💡 二进制分析总结:")
    print(f"• 这种方法适用于文本水印或简单图像水印")
    print(f"• 对于复杂的视频水印，建议使用FFmpeg的视频处理方法")
    print(f"• 二进制修改有风险，可能导致文件损坏")
    print(f"• 最佳方案仍然是使用专业的视频处理工具")

if __name__ == "__main__":
    main()
