#!/usr/bin/env python3
"""
质量测试和对比工具
生成测试片段和质量报告
"""

import subprocess
import os
import json
from datetime import datetime

def extract_test_clips(input_files, output_dir, start_time=30, duration=10):
    """
    从每个文件提取测试片段用于对比
    """
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"🎬 提取测试片段 (从{start_time}s开始，时长{duration}s)")
    
    clips = []
    
    for input_file in input_files:
        if not os.path.exists(input_file):
            continue
            
        filename = os.path.basename(input_file)
        name_without_ext = os.path.splitext(filename)[0]
        clip_path = os.path.join(output_dir, f"clip_{name_without_ext}.mp4")
        
        cmd = [
            'ffmpeg', '-i', input_file,
            '-ss', str(start_time),
            '-t', str(duration),
            '-c', 'copy',
            '-y',
            clip_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✅ {filename} -> {os.path.basename(clip_path)}")
                clips.append(clip_path)
            else:
                print(f"   ❌ {filename} 提取失败")
        except Exception as e:
            print(f"   ❌ {filename} 错误: {e}")
    
    return clips

def create_side_by_side_comparison(files, output_path):
    """
    创建并排对比视频
    """
    if len(files) < 2:
        print("❌ 需要至少2个文件进行对比")
        return False
    
    print(f"🔄 创建并排对比视频...")
    
    # 创建2x2或1x2的网格布局
    if len(files) <= 2:
        # 1x2 布局
        filter_complex = f'[0:v][1:v]hstack=inputs=2[v]'
        inputs = files[:2]
    elif len(files) <= 4:
        # 2x2 布局
        filter_complex = f'[0:v][1:v]hstack=inputs=2[top];[2:v][3:v]hstack=inputs=2[bottom];[top][bottom]vstack=inputs=2[v]'
        inputs = files[:4]
    else:
        inputs = files[:2]
        filter_complex = f'[0:v][1:v]hstack=inputs=2[v]'
    
    cmd = ['ffmpeg'] + [item for file in inputs for item in ['-i', file]]
    cmd.extend([
        '-filter_complex', filter_complex,
        '-map', '[v]',
        '-c:v', 'libx264',
        '-preset', 'fast',
        '-crf', '20',
        '-y',
        output_path
    ])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ 对比视频已创建: {os.path.basename(output_path)}")
            return True
        else:
            print(f"   ❌ 对比视频创建失败")
            return False
    except Exception as e:
        print(f"   ❌ 创建对比视频错误: {e}")
        return False

def analyze_video_quality(video_path):
    """
    分析视频质量指标
    """
    try:
        # 获取基本信息
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            return None
        
        info = json.loads(result.stdout)
        
        video_stream = None
        for stream in info['streams']:
            if stream['codec_type'] == 'video':
                video_stream = stream
                break
        
        if not video_stream:
            return None
        
        # 计算文件大小
        file_size = os.path.getsize(video_path)
        duration = float(info['format'].get('duration', 0))
        
        quality_info = {
            'file_size_mb': file_size / (1024 * 1024),
            'duration': duration,
            'bitrate': int(video_stream.get('bit_rate', 0)) if video_stream.get('bit_rate') else 0,
            'width': int(video_stream.get('width', 0)),
            'height': int(video_stream.get('height', 0)),
            'fps': eval(video_stream.get('r_frame_rate', '25/1')),
            'codec': video_stream.get('codec_name', 'unknown'),
            'pixel_format': video_stream.get('pix_fmt', 'unknown')
        }
        
        # 计算压缩效率
        if duration > 0:
            quality_info['mbps'] = (file_size * 8) / (1024 * 1024 * duration)
        else:
            quality_info['mbps'] = 0
        
        return quality_info
        
    except Exception as e:
        print(f"❌ 分析视频质量失败: {e}")
        return None

def create_quality_report(files):
    """
    创建质量报告
    """
    print(f"📊 生成质量报告...")
    
    report = []
    
    for file_path in files:
        if not os.path.exists(file_path):
            continue
        
        filename = os.path.basename(file_path)
        quality = analyze_video_quality(file_path)
        
        if quality:
            report.append({
                'filename': filename,
                'quality': quality
            })
    
    # 排序：按文件大小
    report.sort(key=lambda x: x['quality']['file_size_mb'], reverse=True)
    
    print(f"\n📋 质量对比报告:")
    print("-" * 100)
    print(f"{'文件名':<35} {'大小(MB)':<10} {'码率(Mbps)':<12} {'分辨率':<12} {'编码':<8}")
    print("-" * 100)
    
    for item in report:
        filename = item['filename']
        q = item['quality']
        
        # 截断文件名
        display_name = filename[:32] + "..." if len(filename) > 35 else filename
        
        print(f"{display_name:<35} {q['file_size_mb']:<10.2f} {q['mbps']:<12.2f} {q['width']}x{q['height']:<6} {q['codec']:<8}")
    
    return report

def create_watermark_test_frames(files, output_dir):
    """
    提取水印区域的测试帧
    """
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"🖼️  提取水印区域测试帧...")
    
    # 水印区域坐标 (基于之前的分析)
    watermark_x, watermark_y = 939, 15
    watermark_w, watermark_h = 129, 115
    
    # 扩展区域以便对比
    crop_x = max(0, watermark_x - 50)
    crop_y = max(0, watermark_y - 20)
    crop_w = watermark_w + 100
    crop_h = watermark_h + 40
    
    frames = []
    
    for file_path in files:
        if not os.path.exists(file_path):
            continue
        
        filename = os.path.basename(file_path)
        name_without_ext = os.path.splitext(filename)[0]
        frame_path = os.path.join(output_dir, f"watermark_area_{name_without_ext}.png")
        
        cmd = [
            'ffmpeg', '-i', file_path,
            '-ss', '30',  # 30秒处提取
            '-vframes', '1',
            '-vf', f'crop={crop_w}:{crop_h}:{crop_x}:{crop_y}',
            '-y',
            frame_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✅ {filename} -> {os.path.basename(frame_path)}")
                frames.append(frame_path)
            else:
                print(f"   ❌ {filename} 提取失败")
        except Exception as e:
            print(f"   ❌ {filename} 错误: {e}")
    
    return frames

def main():
    print("🔍 视频质量测试和对比工具")
    print("=" * 60)
    
    # 要测试的文件列表
    test_files = [
        "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4",  # 原始文件
        "/Users/<USER>/Work/Script/temp3/final_watermark_only.mp4",
        "/Users/<USER>/Work/Script/temp3/advanced_delogo.mp4",
        "/Users/<USER>/Work/Script/temp3/final_both_hybrid.mp4",
        "/Users/<USER>/Work/Script/temp3/advanced_bgfill.mp4"
    ]
    
    # 过滤存在的文件
    existing_files = [f for f in test_files if os.path.exists(f)]
    
    print(f"📁 找到 {len(existing_files)} 个文件进行测试")
    
    # 1. 创建质量报告
    quality_report = create_quality_report(existing_files)
    
    # 2. 提取测试片段
    clips_dir = "/Users/<USER>/Work/Script/temp3/test_clips"
    test_clips = extract_test_clips(existing_files, clips_dir)
    
    # 3. 创建并排对比
    if len(test_clips) >= 2:
        comparison_path = "/Users/<USER>/Work/Script/temp3/side_by_side_comparison.mp4"
        create_side_by_side_comparison(test_clips, comparison_path)
    
    # 4. 提取水印区域对比帧
    frames_dir = "/Users/<USER>/Work/Script/temp3/watermark_frames"
    watermark_frames = create_watermark_test_frames(existing_files, frames_dir)
    
    # 5. 生成最终建议
    print(f"\n🎯 最终测试结果和建议:")
    print("-" * 60)
    
    if quality_report:
        best_quality = min(quality_report, key=lambda x: x['quality']['file_size_mb'])
        best_size = max(quality_report, key=lambda x: x['quality']['file_size_mb'])
        
        print(f"📈 质量分析:")
        print(f"   • 最小文件: {best_quality['filename']} ({best_quality['quality']['file_size_mb']:.2f} MB)")
        print(f"   • 最大文件: {best_size['filename']} ({best_size['quality']['file_size_mb']:.2f} MB)")
        
        # 找到推荐文件
        recommended = None
        for item in quality_report:
            if 'final_watermark_only' in item['filename']:
                recommended = item
                break
        
        if recommended:
            q = recommended['quality']
            print(f"\n🏆 推荐文件: {recommended['filename']}")
            print(f"   • 文件大小: {q['file_size_mb']:.2f} MB")
            print(f"   • 视频码率: {q['mbps']:.2f} Mbps")
            print(f"   • 分辨率: {q['width']}x{q['height']}")
            print(f"   • 帧率: {q['fps']:.2f} fps")
    
    print(f"\n📂 生成的测试文件:")
    print(f"   • 测试片段: {clips_dir}/")
    print(f"   • 水印区域帧: {frames_dir}/")
    if len(test_clips) >= 2:
        print(f"   • 并排对比: side_by_side_comparison.mp4")
    
    print(f"\n💡 使用建议:")
    print(f"   1. 播放 final_watermark_only.mp4 查看整体效果")
    print(f"   2. 查看 watermark_frames/ 中的帧对比水印去除效果")
    print(f"   3. 播放 side_by_side_comparison.mp4 进行直观对比")
    print(f"   4. 根据需求选择最适合的版本")
    
    print(f"\n✨ 任务完成!")

if __name__ == "__main__":
    main()
