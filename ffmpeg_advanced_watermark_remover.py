#!/usr/bin/env python3
"""
FFmpeg高级水印去除工具 - 真正去除水印而非裁剪
使用FFmpeg的高级滤镜实现智能水印修复
"""

import subprocess
import os
import json
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def get_precise_watermark_region(width, height):
    """
    基于FlexTV水印特征的精确定位
    根据您提供的截图分析
    """
    # FlexTV水印通常在右上角，包含播放按钮图标
    # 根据1080x1920的比例调整
    watermark_width = int(width * 0.12)  # 约12%宽度
    watermark_height = int(height * 0.06)  # 约6%高度
    watermark_x = width - watermark_width - 15  # 距离右边15像素
    watermark_y = 15  # 距离顶部15像素
    
    return {
        'x': watermark_x,
        'y': watermark_y,
        'width': watermark_width,
        'height': watermark_height
    }

def remove_watermark_optimized_delogo(input_path, output_path, watermark_region):
    """
    优化的delogo方法 - 最有效的水印去除
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={x}:y={y}:w={w}:h={h}:show=0',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 优化Delogo去除水印")
    print(f"   水印区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "优化Delogo去除水印")

def remove_watermark_smart_blur(input_path, output_path, watermark_region):
    """
    智能模糊修复 - 只对水印区域进行智能模糊
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 使用条件模糊，只对水印区域生效
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'boxblur=10:1:enable=\'between(x,{x},{x+w})*between(y,{y},{y+h})\'',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🌫️  智能模糊去除水印")
    print(f"   模糊区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "智能模糊去除水印")

def remove_watermark_background_fill(input_path, output_path, watermark_region):
    """
    背景填充方法 - 用周围背景填充水印区域
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 扩展区域用于背景采样
    expand_size = 30
    sample_x = max(0, x - expand_size)
    sample_y = max(0, y - expand_size)
    sample_w = w + expand_size * 2
    sample_h = h + expand_size * 2
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'split[main][bg];[bg]crop={sample_w}:{sample_h}:{sample_x}:{sample_y},boxblur=15:1,crop={w}:{h}:{expand_size}:{expand_size}[fill];[main][fill]overlay={x}:{y}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 背景填充去除水印")
    print(f"   填充区域: ({x}, {y}) - {w}x{h}")
    print(f"   采样区域: ({sample_x}, {sample_y}) - {sample_w}x{sample_h}")
    
    return run_ffmpeg_with_progress(cmd, "背景填充去除水印")

def remove_watermark_edge_blend(input_path, output_path, watermark_region):
    """
    边缘融合方法 - 与周围像素自然融合
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 使用unsharp增强边缘，然后用delogo修复
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'unsharp=5:5:0.8:3:3:0.4,delogo=x={x}:y={y}:w={w}:h={h}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🔍 边缘融合去除水印")
    print(f"   处理区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "边缘融合去除水印")

def remove_watermark_multi_stage(input_path, output_path, watermark_region):
    """
    多阶段处理 - 先delogo再平滑
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 多阶段处理：delogo + 轻微模糊平滑
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={x}:y={y}:w={w}:h={h},boxblur=3:1:enable=\'between(x,{x},{x+w})*between(y,{y},{y+h})\'',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🔄 多阶段去除水印")
    print(f"   处理区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "多阶段去除水印")

def remove_watermark_adaptive_cover(input_path, output_path, watermark_region):
    """
    自适应覆盖 - 使用半透明覆盖融合
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 使用半透明的深色覆盖，模拟背景色
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x={x}:y={y}:w={w}:h={h}:color=0x1a1a1a@0.7:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎭 自适应覆盖去除水印")
    print(f"   覆盖区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "自适应覆盖去除水印")

def remove_watermark_texture_synthesis(input_path, output_path, watermark_region):
    """
    纹理合成方法 - 基于周围纹理重建
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 使用noise和blend来模拟纹理合成
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'split[main][texture];[texture]crop={w*2}:{h*2}:{max(0,x-w//2)}:{max(0,y-h//2)},noise=alls=20:allf=t+u,crop={w}:{h}:{w//2}:{h//2}[synth];[main][synth]overlay={x}:{y}:eval=frame',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🧩 纹理合成去除水印")
    print(f"   合成区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "纹理合成去除水印")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🎬 FFmpeg高级水印去除工具 (真正修复，非裁剪)")
    print("=" * 60)

    # 使用原始视频文件
    input_video = "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 获取精确的水印区域
    watermark_region = get_precise_watermark_region(width, height)
    print(f"🎯 FlexTV水印区域: ({watermark_region['x']}, {watermark_region['y']}) - {watermark_region['width']}x{watermark_region['height']}")
    
    # 定义处理方法
    methods = [
        {
            'name': 'optimized_delogo',
            'description': '优化Delogo修复',
            'output': '/Users/<USER>/Work/Script/temp3/advanced_delogo.mp4',
            'function': lambda: remove_watermark_optimized_delogo(input_video, '/Users/<USER>/Work/Script/temp3/advanced_delogo.mp4', watermark_region)
        },
        {
            'name': 'smart_blur',
            'description': '智能模糊修复',
            'output': '/Users/<USER>/Work/Script/temp3/advanced_blur.mp4',
            'function': lambda: remove_watermark_smart_blur(input_video, '/Users/<USER>/Work/Script/temp3/advanced_blur.mp4', watermark_region)
        },
        {
            'name': 'background_fill',
            'description': '背景填充修复',
            'output': '/Users/<USER>/Work/Script/temp3/advanced_bgfill.mp4',
            'function': lambda: remove_watermark_background_fill(input_video, '/Users/<USER>/Work/Script/temp3/advanced_bgfill.mp4', watermark_region)
        },
        {
            'name': 'edge_blend',
            'description': '边缘融合修复',
            'output': '/Users/<USER>/Work/Script/temp3/advanced_edge.mp4',
            'function': lambda: remove_watermark_edge_blend(input_video, '/Users/<USER>/Work/Script/temp3/advanced_edge.mp4', watermark_region)
        },
        {
            'name': 'multi_stage',
            'description': '多阶段修复',
            'output': '/Users/<USER>/Work/Script/temp3/advanced_multistage.mp4',
            'function': lambda: remove_watermark_multi_stage(input_video, '/Users/<USER>/Work/Script/temp3/advanced_multistage.mp4', watermark_region)
        },
        {
            'name': 'adaptive_cover',
            'description': '自适应覆盖',
            'output': '/Users/<USER>/Work/Script/temp3/advanced_cover.mp4',
            'function': lambda: remove_watermark_adaptive_cover(input_video, '/Users/<USER>/Work/Script/temp3/advanced_cover.mp4', watermark_region)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print("-" * 70)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<20}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                         📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 推荐
    print(f"\n🏆 推荐:")
    print("-" * 70)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("   1. advanced_delogo.mp4 (优化Delogo，最推荐)")
        print("   2. advanced_bgfill.mp4 (背景填充)")
        print("   3. advanced_multistage.mp4 (多阶段处理)")
        print("   4. advanced_edge.mp4 (边缘融合)")
        print("   5. advanced_blur.mp4 (智能模糊)")
        print("   6. advanced_cover.mp4 (自适应覆盖)")
        
        print(f"\n💡 关键改进:")
        print("• 精确定位FlexTV水印位置")
        print("• 真正的修复算法，保持画面完整性")
        print("• 不改变视频比例和分辨率")
        print("• 多种算法适应不同场景")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装")
    
    print(f"\n🎉 高级水印去除完成!")

if __name__ == "__main__":
    main()
