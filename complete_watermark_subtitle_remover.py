#!/usr/bin/env python3
"""
同时去除FlexTV水印和底部字幕的综合脚本
"""

import subprocess
import os
import json
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def remove_both_watermark_and_subtitle_crop(input_path, output_path, width, height):
    """
    通过裁剪同时去除右上角水印和底部字幕
    """
    # 右上角水印区域 - 裁剪掉右边15%
    watermark_crop_width = int(width * 0.85)  # 保留左边85%
    
    # 底部字幕区域 - 裁剪掉底部20%
    subtitle_crop_height = int(height * 0.80)  # 保留上面80%
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={watermark_crop_width}:{subtitle_crop_height}:0:0,scale={width}:{height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"✂️  裁剪去除水印和字幕")
    print(f"   原始尺寸: {width}x{height}")
    print(f"   裁剪后尺寸: {watermark_crop_width}x{subtitle_crop_height}")
    print(f"   缩放回: {width}x{height}")
    print(f"   去除右边: {width - watermark_crop_width}px ({(width - watermark_crop_width)/width*100:.1f}%)")
    print(f"   去除底部: {height - subtitle_crop_height}px ({(height - subtitle_crop_height)/height*100:.1f}%)")
    
    return run_ffmpeg_with_progress(cmd, "裁剪去除水印和字幕")

def remove_both_delogo_method(input_path, output_path, width, height):
    """
    使用delogo方法同时去除右上角水印和底部字幕
    """
    # 右上角水印参数
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 10
    watermark_y = 10
    
    # 底部字幕参数
    subtitle_height = int(height * 0.20)
    subtitle_y = height - subtitle_height
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height},delogo=x=0:y={subtitle_y}:w={width}:h={subtitle_height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 Delogo同时去除水印和字幕")
    print(f"   水印区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")
    print(f"   字幕区域: (0, {subtitle_y}) - {width}x{subtitle_height}")
    
    return run_ffmpeg_with_progress(cmd, "Delogo同时去除水印和字幕")

def remove_both_cover_method(input_path, output_path, width, height):
    """
    使用覆盖方法同时去除右上角水印和底部字幕
    """
    # 右上角水印参数
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 10
    watermark_y = 10
    
    # 底部字幕参数
    subtitle_height = int(height * 0.20)
    subtitle_y = height - subtitle_height
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height}:color=black@0.8:t=fill,drawbox=x=0:y={subtitle_y}:w={width}:h={subtitle_height}:color=black:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 覆盖同时去除水印和字幕")
    print(f"   水印覆盖: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")
    print(f"   字幕覆盖: (0, {subtitle_y}) - {width}x{subtitle_height}")
    
    return run_ffmpeg_with_progress(cmd, "覆盖同时去除水印和字幕")

def remove_watermark_only_crop(input_path, output_path, width, height):
    """
    只去除右上角水印（裁剪方法）
    """
    watermark_crop_width = int(width * 0.85)  # 保留左边85%
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={watermark_crop_width}:{height}:0:0,scale={width}:{height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"✂️  只去除右上角水印")
    print(f"   裁剪宽度: {watermark_crop_width} (去除右边{width - watermark_crop_width}px)")
    
    return run_ffmpeg_with_progress(cmd, "只去除右上角水印")

def remove_subtitle_only_crop(input_path, output_path, width, height):
    """
    只去除底部字幕（裁剪方法）
    """
    subtitle_crop_height = int(height * 0.80)  # 保留上面80%
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={width}:{subtitle_crop_height}:0:0,scale={width}:{height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"✂️  只去除底部字幕")
    print(f"   裁剪高度: {subtitle_crop_height} (去除底部{height - subtitle_crop_height}px)")
    
    return run_ffmpeg_with_progress(cmd, "只去除底部字幕")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🎬 FlexTV水印+字幕综合去除工具")
    print("=" * 50)

    # 使用原始视频文件
    input_video = "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 定义处理方法
    methods = [
        {
            'name': 'both_crop',
            'description': '裁剪去除水印+字幕',
            'output': '/Users/<USER>/Work/Script/temp3/clean_both_crop.mp4',
            'function': lambda: remove_both_watermark_and_subtitle_crop(input_video, '/Users/<USER>/Work/Script/temp3/clean_both_crop.mp4', width, height)
        },
        {
            'name': 'both_delogo',
            'description': 'Delogo去除水印+字幕',
            'output': '/Users/<USER>/Work/Script/temp3/clean_both_delogo.mp4',
            'function': lambda: remove_both_delogo_method(input_video, '/Users/<USER>/Work/Script/temp3/clean_both_delogo.mp4', width, height)
        },
        {
            'name': 'both_cover',
            'description': '覆盖去除水印+字幕',
            'output': '/Users/<USER>/Work/Script/temp3/clean_both_cover.mp4',
            'function': lambda: remove_both_cover_method(input_video, '/Users/<USER>/Work/Script/temp3/clean_both_cover.mp4', width, height)
        },
        {
            'name': 'watermark_only',
            'description': '只去除水印',
            'output': '/Users/<USER>/Work/Script/temp3/clean_watermark_only.mp4',
            'function': lambda: remove_watermark_only_crop(input_video, '/Users/<USER>/Work/Script/temp3/clean_watermark_only.mp4', width, height)
        },
        {
            'name': 'subtitle_only',
            'description': '只去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/clean_subtitle_only.mp4',
            'function': lambda: remove_subtitle_only_crop(input_video, '/Users/<USER>/Work/Script/temp3/clean_subtitle_only.mp4', width, height)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print("-" * 70)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<20}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                         📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 推荐
    print(f"\n🏆 推荐:")
    print("-" * 70)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("   1. clean_both_crop.mp4 (同时去除水印和字幕，效果最彻底)")
        print("   2. clean_both_delogo.mp4 (智能修复方法)")
        print("   3. clean_both_cover.mp4 (覆盖方法)")
        print("   4. clean_watermark_only.mp4 (只去除水印)")
        print("   5. clean_subtitle_only.mp4 (只去除字幕)")
        
        print(f"\n💡 使用建议:")
        print("• 裁剪方法会改变画面比例但去除最彻底")
        print("• 如果只需要去除其中一个，可以使用单独的版本")
        print("• Delogo方法尝试智能修复，但可能不完美")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装和视频文件")
    
    print(f"\n🎉 处理完成!")

if __name__ == "__main__":
    main()
