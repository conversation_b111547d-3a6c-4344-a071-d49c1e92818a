#!/usr/bin/env python3
"""
专门去除右上角Flex TV水印的FFmpeg脚本
"""

import subprocess
import os
import json
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def remove_flex_tv_delogo(input_path, output_path, width, height):
    """
    使用delogo滤镜去除右上角Flex TV水印
    """
    # 右上角水印位置计算
    # 假设水印大约占视频宽度的15%，高度的8%
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20  # 距离右边20像素
    watermark_y = 20  # 距离顶部20像素
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 去除右上角Flex TV水印 (delogo方法)")
    print(f"   水印位置: ({watermark_x}, {watermark_y})")
    print(f"   水印大小: {watermark_width}x{watermark_height}")
    
    return run_ffmpeg_with_progress(cmd, "Delogo去除Flex TV水印")

def remove_flex_tv_crop(input_path, output_path, width, height):
    """
    通过裁剪去除右上角水印，然后缩放回原尺寸
    """
    # 裁剪掉右上角的水印区域
    crop_width = width - int(width * 0.15)  # 裁剪掉右边15%
    crop_height = height - int(height * 0.08)  # 裁剪掉顶部8%
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={crop_width}:{crop_height}:0:{int(height * 0.08)},scale={width}:{height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"✂️  裁剪去除右上角Flex TV水印")
    print(f"   裁剪后尺寸: {crop_width}x{crop_height}")
    print(f"   缩放回: {width}x{height}")
    
    return run_ffmpeg_with_progress(cmd, "裁剪去除Flex TV水印")

def remove_flex_tv_blur(input_path, output_path, width, height):
    """
    使用模糊滤镜去除右上角Flex TV水印
    """
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20
    watermark_y = 20
    
    # 创建一个遮罩，只对水印区域进行模糊
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'boxblur=15:1:cr=0:ar=0:enable=\'between(x,{watermark_x},{watermark_x + watermark_width})*between(y,{watermark_y},{watermark_y + watermark_height})\'',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🌫️  模糊去除右上角Flex TV水印")
    print(f"   模糊区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")
    
    return run_ffmpeg_with_progress(cmd, "模糊去除Flex TV水印")

def remove_flex_tv_cover(input_path, output_path, width, height):
    """
    使用纯色覆盖去除右上角Flex TV水印
    """
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20
    watermark_y = 20
    
    # 使用drawbox在水印位置绘制一个与背景相近的颜色块
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height}:color=black@0.8:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 覆盖去除右上角Flex TV水印")
    print(f"   覆盖区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")
    
    return run_ffmpeg_with_progress(cmd, "覆盖去除Flex TV水印")

def remove_flex_tv_advanced_delogo(input_path, output_path, width, height):
    """
    高级delogo，处理多个可能的水印位置
    """
    # 主要水印位置（右上角）
    main_x = width - int(width * 0.15) - 20
    main_y = 20
    main_w = int(width * 0.15)
    main_h = int(height * 0.08)
    
    # 可能的小logo位置（右上角更小的区域）
    small_x = width - int(width * 0.08) - 10
    small_y = 10
    small_w = int(width * 0.08)
    small_h = int(height * 0.04)
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={main_x}:y={main_y}:w={main_w}:h={main_h},delogo=x={small_x}:y={small_y}:w={small_w}:h={small_h}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 高级delogo去除Flex TV水印")
    print(f"   主要区域: ({main_x}, {main_y}) - {main_w}x{main_h}")
    print(f"   次要区域: ({small_x}, {small_y}) - {small_w}x{small_h}")
    
    return run_ffmpeg_with_progress(cmd, "高级delogo去除Flex TV水印")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🎬 Flex TV水印专业去除工具 (FFmpeg版)")
    print("=" * 50)

    input_video = "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 定义处理方法
    methods = [
        {
            'name': 'delogo',
            'description': 'Delogo智能修复',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_delogo.mp4',
            'function': lambda: remove_flex_tv_delogo(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_delogo.mp4', width, height)
        },
        {
            'name': 'crop',
            'description': '裁剪+缩放',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_crop.mp4',
            'function': lambda: remove_flex_tv_crop(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_crop.mp4', width, height)
        },
        {
            'name': 'blur',
            'description': '区域模糊',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_blur.mp4',
            'function': lambda: remove_flex_tv_blur(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_blur.mp4', width, height)
        },
        {
            'name': 'advanced',
            'description': '高级Delogo',
            'output': '/Users/<USER>/Work/Script/temp3/flex_tv_removed_advanced.mp4',
            'function': lambda: remove_flex_tv_advanced_delogo(input_video, '/Users/<USER>/Work/Script/temp3/flex_tv_removed_advanced.mp4', width, height)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print("-" * 60)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<15}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                    📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 推荐
    print(f"\n🏆 推荐:")
    print("-" * 60)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("   1. flex_tv_removed_delogo.mp4 (智能修复，通常效果最好)")
        print("   2. flex_tv_removed_advanced.mp4 (多区域处理)")
        print("   3. flex_tv_removed_crop.mp4 (裁剪方法，可能改变画面比例)")
        print("   4. flex_tv_removed_blur.mp4 (模糊方法，保守处理)")
        
        print(f"\n💡 使用建议:")
        print("• 用视频播放器逐一查看效果")
        print("• Delogo方法通常能完全去除水印且不影响画质")
        print("• 如果Delogo效果不理想，可尝试高级Delogo")
        print("• 裁剪方法会略微改变视频比例但效果最彻底")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装和视频文件")
    
    print(f"\n🎉 Flex TV水印去除完成!")

if __name__ == "__main__":
    main()
