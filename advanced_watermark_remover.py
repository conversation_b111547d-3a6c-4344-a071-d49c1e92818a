#!/usr/bin/env python3
"""
高级水印去除工具 - 真正去除水印而非裁剪
使用多种FFmpeg高级滤镜和OpenCV图像修复技术
"""

import subprocess
import os
import json
import cv2
import numpy as np
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def extract_frame_for_analysis(video_path, output_frame_path, timestamp=5):
    """提取一帧用于水印分析"""
    cmd = [
        'ffmpeg', '-i', video_path,
        '-ss', str(timestamp),
        '-vframes', '1',
        '-y',
        output_frame_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 提取帧失败: {e}")
        return False

def detect_watermark_region(frame_path, width, height):
    """
    智能检测水印区域
    基于右上角区域的特征分析
    """
    try:
        # 读取图像
        img = cv2.imread(frame_path)
        if img is None:
            return None
        
        # 定义右上角搜索区域 (右边30%, 上面20%)
        search_width = int(width * 0.30)
        search_height = int(height * 0.20)
        search_x = width - search_width
        search_y = 0
        
        # 提取右上角区域
        roi = img[search_y:search_y + search_height, search_x:search_x + search_width]
        
        # 转换为灰度
        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        
        # 使用边缘检测找到可能的水印边界
        edges = cv2.Canny(gray, 50, 150)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # 找到最大的轮廓（可能是水印）
            largest_contour = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # 转换回全图坐标
            watermark_x = search_x + x - 10  # 添加一些边距
            watermark_y = search_y + y - 10
            watermark_w = w + 20
            watermark_h = h + 20
            
            # 确保不超出边界
            watermark_x = max(0, watermark_x)
            watermark_y = max(0, watermark_y)
            watermark_w = min(watermark_w, width - watermark_x)
            watermark_h = min(watermark_h, height - watermark_y)
            
            return {
                'x': watermark_x,
                'y': watermark_y,
                'width': watermark_w,
                'height': watermark_h
            }
    
    except Exception as e:
        print(f"❌ 水印检测失败: {e}")
    
    # 如果检测失败，返回默认区域
    return {
        'x': int(width * 0.75),
        'y': 10,
        'width': int(width * 0.20),
        'height': int(height * 0.10)
    }

def remove_watermark_advanced_delogo(input_path, output_path, watermark_region):
    """
    使用高级delogo滤镜去除水印
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={x}:y={y}:w={w}:h={h}:show=0',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 高级Delogo去除水印")
    print(f"   水印区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "高级Delogo去除水印")

def remove_watermark_inpaint_style(input_path, output_path, watermark_region):
    """
    使用类似inpaint的方法去除水印
    通过周围像素的平均值填充水印区域
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 使用boxblur和overlay的组合来模拟inpaint效果
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'split[main][blur];[blur]crop={w}:{h}:{x}:{y},boxblur=10:1[blurred];[main][blurred]overlay={x}:{y}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 Inpaint风格去除水印")
    print(f"   修复区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "Inpaint风格去除水印")

def remove_watermark_edge_aware(input_path, output_path, watermark_region):
    """
    边缘感知的水印去除
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 使用unsharp和delogo的组合
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'unsharp=5:5:1.0:5:5:0.0,delogo=x={x}:y={y}:w={w}:h={h}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🔍 边缘感知去除水印")
    print(f"   处理区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "边缘感知去除水印")

def remove_watermark_multi_pass(input_path, output_path, watermark_region):
    """
    多次处理去除水印
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 先用delogo，再用轻微模糊平滑
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={x}:y={y}:w={w}:h={h},boxblur=2:1:cr=0:ar=0:enable=\'between(x,{x},{x+w})*between(y,{y},{y+h})\'',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🔄 多次处理去除水印")
    print(f"   处理区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "多次处理去除水印")

def remove_watermark_background_reconstruction(input_path, output_path, watermark_region):
    """
    背景重建方法去除水印
    """
    x, y, w, h = watermark_region['x'], watermark_region['y'], watermark_region['width'], watermark_region['height']
    
    # 使用周围区域的像素来重建背景
    expand_x = max(0, x - 20)
    expand_y = max(0, y - 20)
    expand_w = w + 40
    expand_h = h + 40
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'split[main][bg];[bg]crop={expand_w}:{expand_h}:{expand_x}:{expand_y},boxblur=8:1,crop={w}:{h}:20:20[reconstructed];[main][reconstructed]overlay={x}:{y}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🏗️  背景重建去除水印")
    print(f"   重建区域: ({x}, {y}) - {w}x{h}")
    
    return run_ffmpeg_with_progress(cmd, "背景重建去除水印")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🎬 高级水印去除工具 (真正去除，非裁剪)")
    print("=" * 60)

    # 使用原始视频文件
    input_video = "/Users/<USER>/Work/Script/temp3/new_downloaded_video.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 提取一帧用于水印分析
    frame_path = "/Users/<USER>/Work/Script/temp3/analysis_frame.jpg"
    print(f"🔍 提取帧进行水印分析...")
    
    if extract_frame_for_analysis(input_video, frame_path):
        print(f"✅ 帧提取成功")
        
        # 智能检测水印区域
        print(f"🎯 智能检测水印区域...")
        watermark_region = detect_watermark_region(frame_path, width, height)
        
        print(f"✅ 检测到水印区域: ({watermark_region['x']}, {watermark_region['y']}) - {watermark_region['width']}x{watermark_region['height']}")
        
        # 清理临时文件
        if os.path.exists(frame_path):
            os.remove(frame_path)
    else:
        print(f"❌ 帧提取失败，使用默认水印区域")
        watermark_region = {
            'x': int(width * 0.75),
            'y': 10,
            'width': int(width * 0.20),
            'height': int(height * 0.10)
        }
    
    # 定义处理方法
    methods = [
        {
            'name': 'advanced_delogo',
            'description': '高级Delogo修复',
            'output': '/Users/<USER>/Work/Script/temp3/watermark_removed_delogo.mp4',
            'function': lambda: remove_watermark_advanced_delogo(input_video, '/Users/<USER>/Work/Script/temp3/watermark_removed_delogo.mp4', watermark_region)
        },
        {
            'name': 'inpaint_style',
            'description': 'Inpaint风格修复',
            'output': '/Users/<USER>/Work/Script/temp3/watermark_removed_inpaint.mp4',
            'function': lambda: remove_watermark_inpaint_style(input_video, '/Users/<USER>/Work/Script/temp3/watermark_removed_inpaint.mp4', watermark_region)
        },
        {
            'name': 'edge_aware',
            'description': '边缘感知修复',
            'output': '/Users/<USER>/Work/Script/temp3/watermark_removed_edge.mp4',
            'function': lambda: remove_watermark_edge_aware(input_video, '/Users/<USER>/Work/Script/temp3/watermark_removed_edge.mp4', watermark_region)
        },
        {
            'name': 'multi_pass',
            'description': '多次处理修复',
            'output': '/Users/<USER>/Work/Script/temp3/watermark_removed_multipass.mp4',
            'function': lambda: remove_watermark_multi_pass(input_video, '/Users/<USER>/Work/Script/temp3/watermark_removed_multipass.mp4', watermark_region)
        },
        {
            'name': 'background_reconstruction',
            'description': '背景重建修复',
            'output': '/Users/<USER>/Work/Script/temp3/watermark_removed_bg_recon.mp4',
            'function': lambda: remove_watermark_background_reconstruction(input_video, '/Users/<USER>/Work/Script/temp3/watermark_removed_bg_recon.mp4', watermark_region)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print("-" * 70)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<20}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                         📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 推荐
    print(f"\n🏆 推荐:")
    print("-" * 70)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("   1. watermark_removed_delogo.mp4 (高级Delogo，通常效果最好)")
        print("   2. watermark_removed_bg_recon.mp4 (背景重建)")
        print("   3. watermark_removed_multipass.mp4 (多次处理)")
        print("   4. watermark_removed_inpaint.mp4 (Inpaint风格)")
        print("   5. watermark_removed_edge.mp4 (边缘感知)")
        
        print(f"\n💡 关键改进:")
        print("• 使用智能检测确定水印精确位置")
        print("• 真正的修复算法，不是简单裁剪")
        print("• 保持原始视频比例和质量")
        print("• 多种算法适应不同水印特征")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装和OpenCV库")
    
    print(f"\n🎉 高级水印去除完成!")

if __name__ == "__main__":
    main()
