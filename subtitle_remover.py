#!/usr/bin/env python3
"""
专门去除视频底部字幕的FFmpeg脚本
"""

import subprocess
import os
import json
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def remove_subtitle_crop(input_path, output_path, width, height):
    """
    通过裁剪去除底部字幕区域，然后缩放回原尺寸
    """
    # 裁剪掉底部的字幕区域（通常占视频高度的10-15%）
    subtitle_height = int(height * 0.12)  # 裁剪掉底部12%
    crop_height = height - subtitle_height
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={width}:{crop_height}:0:0,scale={width}:{height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"✂️  裁剪去除底部字幕")
    print(f"   原始尺寸: {width}x{height}")
    print(f"   裁剪后尺寸: {width}x{crop_height}")
    print(f"   缩放回: {width}x{height}")
    
    return run_ffmpeg_with_progress(cmd, "裁剪去除底部字幕")

def remove_subtitle_delogo(input_path, output_path, width, height):
    """
    使用delogo滤镜去除底部字幕区域
    """
    # 底部字幕区域计算
    subtitle_height = int(height * 0.12)  # 字幕区域高度约12%
    subtitle_y = height - subtitle_height  # 从底部开始
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x=0:y={subtitle_y}:w={width}:h={subtitle_height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 Delogo去除底部字幕")
    print(f"   字幕区域: (0, {subtitle_y}) - {width}x{subtitle_height}")
    
    return run_ffmpeg_with_progress(cmd, "Delogo去除底部字幕")

def remove_subtitle_blur(input_path, output_path, width, height):
    """
    使用模糊滤镜去除底部字幕
    """
    subtitle_height = int(height * 0.12)
    subtitle_y = height - subtitle_height
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'boxblur=15:1:enable=\'between(y,{subtitle_y},{height})\'',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🌫️  模糊去除底部字幕")
    print(f"   模糊区域: 底部 {subtitle_height} 像素")
    
    return run_ffmpeg_with_progress(cmd, "模糊去除底部字幕")

def remove_subtitle_cover(input_path, output_path, width, height):
    """
    使用纯色覆盖去除底部字幕
    """
    subtitle_height = int(height * 0.12)
    subtitle_y = height - subtitle_height
    
    # 使用黑色覆盖字幕区域
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x=0:y={subtitle_y}:w={width}:h={subtitle_height}:color=black:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 覆盖去除底部字幕")
    print(f"   覆盖区域: (0, {subtitle_y}) - {width}x{subtitle_height}")
    
    return run_ffmpeg_with_progress(cmd, "覆盖去除底部字幕")

def remove_both_watermark_and_subtitle(input_path, output_path, width, height):
    """
    同时去除右上角水印和底部字幕
    """
    # 右上角水印参数
    watermark_width = int(width * 0.15)
    watermark_height = int(height * 0.08)
    watermark_x = width - watermark_width - 20
    watermark_y = 20
    
    # 底部字幕参数
    subtitle_height = int(height * 0.12)
    subtitle_y = height - subtitle_height
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'delogo=x={watermark_x}:y={watermark_y}:w={watermark_width}:h={watermark_height},delogo=x=0:y={subtitle_y}:w={width}:h={subtitle_height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎯 同时去除水印和字幕")
    print(f"   水印区域: ({watermark_x}, {watermark_y}) - {watermark_width}x{watermark_height}")
    print(f"   字幕区域: (0, {subtitle_y}) - {width}x{subtitle_height}")
    
    return run_ffmpeg_with_progress(cmd, "同时去除水印和字幕")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("📺 视频字幕去除工具 (FFmpeg版)")
    print("=" * 50)

    # 使用现有的 flex_tv_removed_crop.mp4 作为输入
    input_video = "/Users/<USER>/Work/Script/temp3/flex_tv_removed_crop.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        print("请先运行 flex_tv_watermark_remover.py 生成 flex_tv_removed_crop.mp4")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 定义处理方法
    methods = [
        {
            'name': 'crop',
            'description': '裁剪去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_no_subtitle_crop.mp4',
            'function': lambda: remove_subtitle_crop(input_video, '/Users/<USER>/Work/Script/temp3/final_no_subtitle_crop.mp4', width, height)
        },
        {
            'name': 'delogo',
            'description': 'Delogo去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_no_subtitle_delogo.mp4',
            'function': lambda: remove_subtitle_delogo(input_video, '/Users/<USER>/Work/Script/temp3/final_no_subtitle_delogo.mp4', width, height)
        },
        {
            'name': 'blur',
            'description': '模糊去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_no_subtitle_blur.mp4',
            'function': lambda: remove_subtitle_blur(input_video, '/Users/<USER>/Work/Script/temp3/final_no_subtitle_blur.mp4', width, height)
        },
        {
            'name': 'cover',
            'description': '覆盖去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_no_subtitle_cover.mp4',
            'function': lambda: remove_subtitle_cover(input_video, '/Users/<USER>/Work/Script/temp3/final_no_subtitle_cover.mp4', width, height)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print("-" * 60)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<15}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                    📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 推荐
    print(f"\n🏆 推荐:")
    print("-" * 60)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("   1. final_no_subtitle_crop.mp4 (裁剪方法，效果最彻底)")
        print("   2. final_no_subtitle_delogo.mp4 (智能修复)")
        print("   3. final_no_subtitle_blur.mp4 (模糊方法)")
        print("   4. final_no_subtitle_cover.mp4 (覆盖方法)")
        
        print(f"\n💡 使用建议:")
        print("• 裁剪方法会略微改变视频比例但去除最彻底")
        print("• Delogo方法尝试智能修复字幕区域")
        print("• 模糊和覆盖方法较为保守")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装和视频文件")
    
    print(f"\n🎉 字幕去除完成!")

if __name__ == "__main__":
    main()
