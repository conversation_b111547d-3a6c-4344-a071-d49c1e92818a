#!/usr/bin/env python3
"""
精确去除视频底部字幕的FFmpeg脚本
基于用户反馈调整参数
"""

import subprocess
import os
import json
from datetime import datetime

def get_video_info(video_path):
    """获取视频信息"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            for stream in info['streams']:
                if stream['codec_type'] == 'video':
                    return {
                        'width': int(stream.get('width', 0)),
                        'height': int(stream.get('height', 0)),
                        'fps': eval(stream.get('r_frame_rate', '25/1')),
                        'duration': float(info['format'].get('duration', 0))
                    }
        return None
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return None

def remove_subtitle_precise_crop(input_path, output_path, width, height):
    """
    精确裁剪去除底部字幕区域
    """
    # 根据截图分析，字幕位置大约在底部20%的区域
    subtitle_height = int(height * 0.20)  # 裁剪掉底部20%
    crop_height = height - subtitle_height
    
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'crop={width}:{crop_height}:0:0,scale={width}:{height}',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"✂️  精确裁剪去除底部字幕")
    print(f"   原始尺寸: {width}x{height}")
    print(f"   裁剪后尺寸: {width}x{crop_height}")
    print(f"   缩放回: {width}x{height}")
    print(f"   去除区域: 底部{subtitle_height}像素 ({subtitle_height/height*100:.1f}%)")
    
    return run_ffmpeg_with_progress(cmd, "精确裁剪去除底部字幕")

def remove_subtitle_precise_cover(input_path, output_path, width, height):
    """
    精确覆盖去除底部字幕
    """
    subtitle_height = int(height * 0.20)
    subtitle_y = height - subtitle_height
    
    # 使用黑色覆盖字幕区域
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x=0:y={subtitle_y}:w={width}:h={subtitle_height}:color=black:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🎨 精确覆盖去除底部字幕")
    print(f"   覆盖区域: (0, {subtitle_y}) - {width}x{subtitle_height}")
    print(f"   覆盖区域: 底部{subtitle_height}像素 ({subtitle_height/height*100:.1f}%)")
    
    return run_ffmpeg_with_progress(cmd, "精确覆盖去除底部字幕")

def remove_subtitle_gradient_cover(input_path, output_path, width, height):
    """
    使用渐变覆盖去除底部字幕，看起来更自然
    """
    subtitle_height = int(height * 0.20)
    subtitle_y = height - subtitle_height
    
    # 创建一个从透明到黑色的渐变覆盖
    cmd = [
        'ffmpeg', '-i', input_path,
        '-vf', f'drawbox=x=0:y={subtitle_y}:w={width}:h={subtitle_height//2}:color=black@0.3:t=fill,drawbox=x=0:y={subtitle_y + subtitle_height//2}:w={width}:h={subtitle_height//2}:color=black:t=fill',
        '-c:a', 'copy',
        '-preset', 'medium',
        '-crf', '18',
        '-y',
        output_path
    ]
    
    print(f"🌈 渐变覆盖去除底部字幕")
    print(f"   渐变区域: (0, {subtitle_y}) - {width}x{subtitle_height}")
    
    return run_ffmpeg_with_progress(cmd, "渐变覆盖去除底部字幕")

def run_ffmpeg_with_progress(cmd, operation_name):
    """执行FFmpeg命令并显示进度"""
    print(f"🚀 开始: {operation_name}")
    print(f"📝 命令: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                # 显示包含时间信息的行
                if 'time=' in output and 'speed=' in output:
                    # 提取时间和速度信息
                    parts = output.strip().split()
                    time_info = next((p for p in parts if p.startswith('time=')), '')
                    speed_info = next((p for p in parts if p.startswith('speed=')), '')
                    if time_info and speed_info:
                        print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
        
        print()  # 换行
        
        return_code = process.poll()
        
        if return_code == 0:
            print(f"✅ {operation_name} 完成!")
            return True
        else:
            print(f"❌ {operation_name} 失败!")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🎯 精确字幕去除工具 (FFmpeg版)")
    print("=" * 50)

    # 使用现有的 flex_tv_removed_crop.mp4 作为输入
    input_video = "/Users/<USER>/Work/Script/temp3/flex_tv_removed_crop.mp4"
    
    if not os.path.exists(input_video):
        print(f"❌ 输入视频不存在: {input_video}")
        print("请先运行 flex_tv_watermark_remover.py 生成 flex_tv_removed_crop.mp4")
        return
    
    # 获取视频信息
    print(f"📋 分析视频信息...")
    video_info = get_video_info(input_video)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    width, height = video_info['width'], video_info['height']
    print(f"✅ 视频信息: {width}x{height}, {video_info['fps']:.2f}fps, {video_info['duration']:.1f}s")
    
    # 定义处理方法
    methods = [
        {
            'name': 'precise_crop',
            'description': '精确裁剪去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_precise_crop.mp4',
            'function': lambda: remove_subtitle_precise_crop(input_video, '/Users/<USER>/Work/Script/temp3/final_precise_crop.mp4', width, height)
        },
        {
            'name': 'precise_cover',
            'description': '精确覆盖去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_precise_cover.mp4',
            'function': lambda: remove_subtitle_precise_cover(input_video, '/Users/<USER>/Work/Script/temp3/final_precise_cover.mp4', width, height)
        },
        {
            'name': 'gradient_cover',
            'description': '渐变覆盖去除字幕',
            'output': '/Users/<USER>/Work/Script/temp3/final_gradient_cover.mp4',
            'function': lambda: remove_subtitle_gradient_cover(input_video, '/Users/<USER>/Work/Script/temp3/final_gradient_cover.mp4', width, height)
        }
    ]
    
    # 执行所有方法
    results = []
    for i, method in enumerate(methods, 1):
        print(f"\n🔧 [{i}/{len(methods)}] {method['description']}")
        start_time = datetime.now()
        
        success = method['function']()
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        results.append({
            'method': method['name'],
            'description': method['description'],
            'output': method['output'],
            'success': success,
            'processing_time': processing_time
        })
    
    # 显示结果
    print(f"\n📊 处理结果:")
    print("-" * 60)
    
    successful_files = []
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['description']:<20}: {status} ({result['processing_time']:.1f}s)")
        
        if result['success'] and os.path.exists(result['output']):
            size_mb = os.path.getsize(result['output']) / (1024 * 1024)
            filename = os.path.basename(result['output'])
            print(f"                         📁 {filename} ({size_mb:.2f} MB)")
            successful_files.append(result['output'])
    
    # 推荐
    print(f"\n🏆 推荐:")
    print("-" * 60)
    if successful_files:
        print("✅ 建议按以下顺序测试效果:")
        print("   1. final_precise_crop.mp4 (精确裁剪，效果最彻底)")
        print("   2. final_precise_cover.mp4 (精确覆盖)")
        print("   3. final_gradient_cover.mp4 (渐变覆盖，更自然)")
        
        print(f"\n💡 使用建议:")
        print("• 精确裁剪会去除底部20%区域，确保字幕完全消失")
        print("• 如果裁剪效果满意，推荐使用裁剪版本")
        print("• 覆盖方法保持原始比例但会有黑色区域")
    else:
        print("❌ 所有方法都失败了，请检查FFmpeg安装和视频文件")
    
    print(f"\n🎉 精确字幕去除完成!")

if __name__ == "__main__":
    main()
