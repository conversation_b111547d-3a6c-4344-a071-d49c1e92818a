#!/usr/bin/env python3
"""
通用水印去除工具
基于FlexTV项目的经验，创建一个可重用的水印去除工具
"""

import subprocess
import os
import json
import argparse
from datetime import datetime

class WatermarkRemover:
    def __init__(self):
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.flv']
    
    def get_video_info(self, video_path):
        """获取视频信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                info = json.loads(result.stdout)
                
                for stream in info['streams']:
                    if stream['codec_type'] == 'video':
                        return {
                            'width': int(stream.get('width', 0)),
                            'height': int(stream.get('height', 0)),
                            'fps': eval(stream.get('r_frame_rate', '25/1')),
                            'duration': float(info['format'].get('duration', 0)),
                            'codec': stream.get('codec_name', 'unknown'),
                            'bitrate': int(stream.get('bit_rate', 0)) if stream.get('bit_rate') else 0
                        }
            return None
        except Exception as e:
            print(f"❌ 获取视频信息失败: {e}")
            return None
    
    def remove_watermark_delogo(self, input_path, output_path, x, y, width, height, quality='high'):
        """使用delogo方法去除水印"""
        
        # 质量设置
        quality_settings = {
            'fast': {'preset': 'fast', 'crf': '20'},
            'medium': {'preset': 'medium', 'crf': '18'},
            'high': {'preset': 'slow', 'crf': '16'},
            'best': {'preset': 'veryslow', 'crf': '14'}
        }
        
        settings = quality_settings.get(quality, quality_settings['high'])
        
        cmd = [
            'ffmpeg', '-i', input_path,
            '-vf', f'delogo=x={x}:y={y}:w={width}:h={height}:show=0',
            '-c:a', 'copy',
            '-preset', settings['preset'],
            '-crf', settings['crf'],
            '-pix_fmt', 'yuv420p',
            '-y',
            output_path
        ]
        
        print(f"🎯 使用Delogo去除水印")
        print(f"   区域: ({x}, {y}) - {width}x{height}")
        print(f"   质量: {quality} (CRF={settings['crf']}, preset={settings['preset']})")
        
        return self._run_ffmpeg(cmd, "Delogo水印去除")
    
    def remove_watermark_crop(self, input_path, output_path, crop_left=0, crop_top=0, crop_right=0, crop_bottom=0):
        """使用裁剪方法去除水印"""
        
        # 获取原始视频尺寸
        video_info = self.get_video_info(input_path)
        if not video_info:
            return False
        
        original_width = video_info['width']
        original_height = video_info['height']
        
        # 计算裁剪后的尺寸
        crop_width = original_width - crop_left - crop_right
        crop_height = original_height - crop_top - crop_bottom
        
        cmd = [
            'ffmpeg', '-i', input_path,
            '-vf', f'crop={crop_width}:{crop_height}:{crop_left}:{crop_top},scale={original_width}:{original_height}',
            '-c:a', 'copy',
            '-preset', 'medium',
            '-crf', '18',
            '-y',
            output_path
        ]
        
        print(f"✂️  使用裁剪去除水印")
        print(f"   裁剪: 左{crop_left} 上{crop_top} 右{crop_right} 下{crop_bottom}")
        print(f"   缩放: {crop_width}x{crop_height} -> {original_width}x{original_height}")
        
        return self._run_ffmpeg(cmd, "裁剪水印去除")
    
    def remove_watermark_cover(self, input_path, output_path, x, y, width, height, color='black', opacity=1.0):
        """使用覆盖方法去除水印"""
        
        # 处理透明度
        if opacity < 1.0:
            color_with_alpha = f"{color}@{opacity}"
        else:
            color_with_alpha = color
        
        cmd = [
            'ffmpeg', '-i', input_path,
            '-vf', f'drawbox=x={x}:y={y}:w={width}:h={height}:color={color_with_alpha}:t=fill',
            '-c:a', 'copy',
            '-preset', 'medium',
            '-crf', '18',
            '-y',
            output_path
        ]
        
        print(f"🎨 使用覆盖去除水印")
        print(f"   区域: ({x}, {y}) - {width}x{height}")
        print(f"   颜色: {color} (透明度: {opacity})")
        
        return self._run_ffmpeg(cmd, "覆盖水印去除")
    
    def auto_detect_flextv_watermark(self, video_info):
        """自动检测FlexTV类型的水印位置"""
        width = video_info['width']
        height = video_info['height']
        
        # 基于FlexTV项目的经验
        watermark_params = {
            'x': int(width * 0.87),  # 右边13%处
            'y': 15,                 # 距离顶部15像素
            'width': int(width * 0.12),   # 宽度12%
            'height': int(height * 0.06), # 高度6%
        }
        
        return watermark_params
    
    def extract_test_frame(self, input_path, output_path, timestamp=30):
        """提取测试帧"""
        cmd = [
            'ffmpeg', '-i', input_path,
            '-ss', str(timestamp),
            '-vframes', '1',
            '-y',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"❌ 提取测试帧失败: {e}")
            return False
    
    def _run_ffmpeg(self, cmd, operation_name):
        """执行FFmpeg命令"""
        print(f"🚀 开始: {operation_name}")
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    if 'time=' in output and 'speed=' in output:
                        parts = output.strip().split()
                        time_info = next((p for p in parts if p.startswith('time=')), '')
                        speed_info = next((p for p in parts if p.startswith('speed=')), '')
                        if time_info and speed_info:
                            print(f"\r  ⏱️  {time_info} {speed_info}", end='', flush=True)
            
            print()
            
            return_code = process.poll()
            
            if return_code == 0:
                print(f"✅ {operation_name} 完成!")
                return True
            else:
                print(f"❌ {operation_name} 失败!")
                return False
                
        except Exception as e:
            print(f"❌ 执行命令时出错: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='通用视频水印去除工具')
    parser.add_argument('input', help='输入视频文件路径')
    parser.add_argument('-o', '--output', help='输出视频文件路径')
    parser.add_argument('-m', '--method', choices=['delogo', 'crop', 'cover'], default='delogo', help='去除方法')
    parser.add_argument('-x', type=int, help='水印X坐标')
    parser.add_argument('-y', type=int, help='水印Y坐标')
    parser.add_argument('-w', '--width', type=int, help='水印宽度')
    parser.add_argument('--height', type=int, help='水印高度')
    parser.add_argument('-q', '--quality', choices=['fast', 'medium', 'high', 'best'], default='high', help='输出质量')
    parser.add_argument('--auto-flextv', action='store_true', help='自动检测FlexTV类型水印')
    parser.add_argument('--preview', action='store_true', help='只提取预览帧，不处理视频')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        return
    
    # 创建水印去除器
    remover = WatermarkRemover()
    
    # 获取视频信息
    print("📋 分析视频信息...")
    video_info = remover.get_video_info(args.input)
    
    if not video_info:
        print("❌ 无法获取视频信息")
        return
    
    print(f"✅ 视频信息: {video_info['width']}x{video_info['height']}, {video_info['codec']}, {video_info['duration']:.1f}s")
    
    # 确定输出路径
    if not args.output:
        base_name = os.path.splitext(args.input)[0]
        args.output = f"{base_name}_watermark_removed.mp4"
    
    # 自动检测FlexTV水印
    if args.auto_flextv:
        watermark_params = remover.auto_detect_flextv_watermark(video_info)
        args.x = watermark_params['x']
        args.y = watermark_params['y']
        args.width = watermark_params['width']
        args.height = watermark_params['height']
        print(f"🎯 自动检测到水印位置: ({args.x}, {args.y}) - {args.width}x{args.height}")
    
    # 预览模式
    if args.preview:
        preview_path = f"{os.path.splitext(args.input)[0]}_preview.jpg"
        if remover.extract_test_frame(args.input, preview_path):
            print(f"✅ 预览帧已保存: {preview_path}")
        return
    
    # 检查必要参数
    if args.method in ['delogo', 'cover'] and not all([args.x is not None, args.y is not None, args.width, args.height]):
        print("❌ delogo和cover方法需要指定水印位置参数 (-x, -y, -w, -h) 或使用 --auto-flextv")
        return
    
    # 执行水印去除
    print(f"\n🔧 使用 {args.method} 方法去除水印...")
    
    success = False
    
    if args.method == 'delogo':
        success = remover.remove_watermark_delogo(
            args.input, args.output, 
            args.x, args.y, args.width, args.height, 
            args.quality
        )
    elif args.method == 'crop':
        # 简化的裁剪参数，去除右上角区域
        crop_right = video_info['width'] - args.x if args.x else 0
        crop_top = 0
        success = remover.remove_watermark_crop(
            args.input, args.output,
            crop_right=crop_right, crop_top=crop_top
        )
    elif args.method == 'cover':
        success = remover.remove_watermark_cover(
            args.input, args.output,
            args.x, args.y, args.width, args.height
        )
    
    if success:
        output_size = os.path.getsize(args.output) / (1024 * 1024)
        print(f"\n🎉 水印去除完成!")
        print(f"📁 输出文件: {args.output} ({output_size:.2f} MB)")
        print(f"💡 建议使用视频播放器检查效果")
    else:
        print(f"\n❌ 水印去除失败!")

if __name__ == "__main__":
    main()
